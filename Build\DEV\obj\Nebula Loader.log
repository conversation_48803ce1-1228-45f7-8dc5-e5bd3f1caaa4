﻿  PORTABLE SECURITY BUILD SYSTEM
  ==============================
  Generating new BUILD_SIGNATURE_LEGACY: 0xFBAAE203
  Note: Portable system uses runtime entropy, this is just for fallback
  Successfully updated BUILD_SIGNATURE_LEGACY to: 0xFBAAE203
  
  Build signature update completed.
  debug.cpp
  config.cpp
  security.cpp
  core.cpp
  indirect_crash.cpp
  animation.cpp
  components.cpp
  security_integration.cpp
  security_stubs.cpp
  test_security_dashboard.cpp
  test_security_ui_integration.cpp
  test_window_management.cpp
  test_window_management_enhanced.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_window_management_enhanced.cpp(177,64): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_window_management_enhanced.cpp(177,49): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_window_management_enhanced.cpp(190,60): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_window_management_enhanced.cpp(190,45): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  theme.cpp
  ui.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\ui.cpp(772,24): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  imgui.cpp
  imgui_demo.cpp
  imgui_draw.cpp
  imgui_impl_dx9.cpp
  imgui_impl_win32.cpp
  Compiling...
  imgui_tables.cpp
  imgui_widgets.cpp
  Main.cpp
  Generating code
  743 of 3352 functions (22.2%) were compiled, the rest were copied from previous compilation.
    222 functions were new in current compilation
    210 functions had inline decision re-evaluated but remain unchanged
  Finished generating code
  Nebula Loader.vcxproj -> C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\Build\DEV\Nebula Loader.exe
