﻿  PORTABLE SECURITY BUILD SYSTEM
  ==============================
  Generating new BUILD_SIGNATURE_LEGACY: 0x09A92EAF
  Note: Portable system uses runtime entropy, this is just for fallback
  Successfully updated BUILD_SIGNATURE_LEGACY to: 0x09A92EAF
  
  Build signature update completed.
  Main.cpp
  config.cpp
  core.cpp
  debug.cpp
  security.cpp
  test_security_ui_integration.cpp
  test_window_management_enhanced.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_window_management_enhanced.cpp(177,64): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_window_management_enhanced.cpp(177,49): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_window_management_enhanced.cpp(190,60): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_window_management_enhanced.cpp(190,45): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  Generating code
  1571 of 3811 functions (41.2%) were compiled, the rest were copied from previous compilation.
    877 functions were new in current compilation
    256 functions had inline decision re-evaluated but remain unchanged
  Finished generating code
  Nebula Loader.vcxproj -> C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\Build\DEV\Nebula Loader.exe
