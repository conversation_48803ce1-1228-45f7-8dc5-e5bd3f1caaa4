﻿  PORTABLE SECURITY BUILD SYSTEM
  ==============================
  Generating new BUILD_SIGNATURE_LEGACY: 0x122E8732
  Note: Portable system uses runtime entropy, this is just for fallback
  Successfully updated BUILD_SIGNATURE_LEGACY to: 0x122E8732
  
  Build signature update completed.
  Main.cpp
  config.cpp
  core.cpp
  debug.cpp
  security.cpp
test_window_management_enhanced.obj : error LNK2005: main already defined in test_security_ui_integration.obj
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\Build\DEV\Nebula Loader.exe : fatal error LNK1169: one or more multiply defined symbols found
