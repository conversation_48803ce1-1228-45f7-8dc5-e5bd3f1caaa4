// Prevent Windows socket header conflicts
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#ifndef NOMINMAX
#define NOMINMAX
#endif

#include "components.hpp"
#include "theme.hpp"
#include "animation.hpp"
#include <chrono>
#include <algorithm>
#include <sstream>
#include <vector>

// Prevent Windows macro conflicts
#ifdef min
#undef min
#endif
#ifdef max
#undef max
#endif

namespace ui {

    // ModernButton implementation
    void ModernButton::Render() {
        Render(ImVec2(0, 0));
    }
    
    bool ModernButton::Render(const ImVec2& size) {
        if (!m_enabled) {
            ImGui::PushStyleVar(ImGuiStyleVar_Alpha, 0.5f);
        }

        const auto& theme = Theme::GetCurrentTheme();
        
        // Get current time for animations
        static auto startTime = std::chrono::steady_clock::now();
        auto currentTime = std::chrono::steady_clock::now();
        float deltaTime = std::chrono::duration<float>(currentTime - startTime).count();
        startTime = currentTime;

        // Handle hover animation
        bool isHovered = ImGui::IsItemHovered() && m_enabled;
        m_hoverAnimation = Animation::Transitions::SmoothHover(m_hoverAnimation, isHovered, 8.0f, deltaTime);

        // Determine button colors based on style
        ImVec4 baseColor, hoverColor, activeColor;
        switch (m_style) {
            case Style::Primary:
                baseColor = theme.primary;
                hoverColor = theme.primaryHover;
                activeColor = theme.primaryActive;
                break;
            case Style::Secondary:
                baseColor = theme.secondary;
                hoverColor = theme.secondaryHover;
                activeColor = Theme::AdjustBrightness(theme.secondary, 0.8f);
                break;
            case Style::Danger:
                baseColor = theme.error;
                hoverColor = Theme::AdjustBrightness(theme.error, 1.2f);
                activeColor = Theme::AdjustBrightness(theme.error, 0.8f);
                break;
        }

        // Blend colors based on hover animation
        ImVec4 currentColor = Theme::BlendColors(baseColor, hoverColor, m_hoverAnimation);

        // Apply button styling
        ImGui::PushStyleColor(ImGuiCol_Button, currentColor);
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, hoverColor);
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, activeColor);
        
        // Add subtle border for secondary buttons
        if (m_style == Style::Secondary) {
            ImGui::PushStyleColor(ImGuiCol_Border, theme.border);
            ImGui::PushStyleVar(ImGuiStyleVar_FrameBorderSize, 1.0f);
        }

        // Render the button
        bool clicked = ImGui::Button(m_label.c_str(), size) && m_enabled;

        // Pop styling
        if (m_style == Style::Secondary) {
            ImGui::PopStyleVar(); // FrameBorderSize
            ImGui::PopStyleColor(); // Border
        }
        ImGui::PopStyleColor(3); // Button colors

        if (!m_enabled) {
            ImGui::PopStyleVar(); // Alpha
        }

        return clicked;
    }

    // StatusIndicator implementation
    void StatusIndicator::Render() {
        const auto& theme = Theme::GetCurrentTheme();
        
        // Get current time for animations
        static auto startTime = std::chrono::steady_clock::now();
        auto currentTime = std::chrono::steady_clock::now();
        float globalTime = std::chrono::duration<float>(currentTime - startTime).count();

        // Update pulse animation
        if (m_animated) {
            float deltaTime = 0.016f; // Approximate 60fps
            m_pulseAnimation += deltaTime;
        }

        // Determine status color and properties
        ImVec4 statusColor;
        const char* statusText = "";
        const char* statusIcon = "";
        bool shouldPulse = false;
        
        switch (m_status) {
            case Status::Unknown:
                statusColor = theme.textSecondary;
                statusText = "Unknown";
                statusIcon = "?";
                break;
            case Status::Checking:
                statusColor = theme.warning;
                statusText = "Checking...";
                statusIcon = "~";
                shouldPulse = true;
                break;
            case Status::Secure:
                statusColor = theme.success;
                statusText = "Secure";
                statusIcon = "✓";
                break;
            case Status::Warning:
                statusColor = theme.warning;
                statusText = "Warning";
                statusIcon = "!";
                shouldPulse = true;
                break;
            case Status::Critical:
                statusColor = theme.error;
                statusText = "Critical";
                statusIcon = "✗";
                shouldPulse = true;
                break;
        }

        // Apply pulsing animation for active states
        ImVec4 currentColor = statusColor;
        if (m_animated && shouldPulse) {
            float pulse = Animation::Transitions::PulseAnimation(globalTime, 2.0f, 0.3f);
            currentColor = Theme::AdjustBrightness(statusColor, pulse);
        }

        // Render status indicator
        ImDrawList* drawList = ImGui::GetWindowDrawList();
        ImVec2 pos = ImGui::GetCursorScreenPos();
        float radius = m_size;
        
        // Draw outer glow for critical/warning states
        if (m_glowIntensity > 0.01f) {
            float glowRadius = radius + 4.0f + 2.0f * std::sin(globalTime * 3.0f) * m_glowIntensity;
            drawList->AddCircle(
                ImVec2(pos.x + radius + 4.0f, pos.y + radius + 4.0f), 
                glowRadius, 
                ImGui::ColorConvertFloat4ToU32(Theme::WithAlpha(currentColor, 0.3f * m_glowIntensity)), 
                16, 
                2.0f
            );
        }
        
        // Draw main status circle with gradient effect
        ImVec4 centerColor = Theme::AdjustBrightness(currentColor, 1.2f);
        drawList->AddCircleFilled(
            ImVec2(pos.x + radius + 4.0f, pos.y + radius + 4.0f), 
            radius, 
            ImGui::ColorConvertFloat4ToU32(currentColor)
        );
        
        // Add inner highlight for 3D effect
        drawList->AddCircleFilled(
            ImVec2(pos.x + radius + 2.0f, pos.y + radius + 2.0f), 
            radius * 0.4f, 
            ImGui::ColorConvertFloat4ToU32(Theme::WithAlpha(centerColor, 0.6f))
        );

        // Draw status icon in the center
        ImVec2 iconPos = ImVec2(pos.x + radius - 2.0f, pos.y + radius - 6.0f);
        drawList->AddText(iconPos, ImGui::ColorConvertFloat4ToU32(ImVec4(1.0f, 1.0f, 1.0f, 1.0f)), statusIcon);

        // Move cursor past the indicator
        ImGui::SetCursorPos(ImVec2(ImGui::GetCursorPosX() + (radius + 4.0f) * 2 + 8.0f, ImGui::GetCursorPosY()));
        
        // Render status text if enabled
        if (m_showText) {
            ImGui::PushStyleColor(ImGuiCol_Text, currentColor);
            ImGui::Text("%s", statusText);
            ImGui::PopStyleColor();
            
            // Add subtle animation to text for checking state
            if (m_status == Status::Checking && m_animated) {
                ImGui::SameLine();
                int dots = (int)(globalTime * 2.0f) % 4;
                for (int i = 0; i < dots; i++) {
                    ImGui::SameLine(0, 0);
                    ImGui::TextColored(Theme::WithAlpha(currentColor, 0.7f), ".");
                }
            }
        }
    }

    void StatusIndicator::Update(float deltaTime) {
        if (!m_animated) return;
        
        // Update pulse animation
        m_pulseAnimation += deltaTime;
        
        // Update glow intensity for animated states
        bool shouldGlow = (m_status == Status::Checking || m_status == Status::Critical || m_status == Status::Warning);
        float targetGlow = shouldGlow ? 1.0f : 0.0f;
        m_glowIntensity = Animation::GetAnimationSystem().SmoothStep(m_glowIntensity, targetGlow, 4.0f, deltaTime);
    }

    void HeaderComponent::SetSecurityStatus(const std::string& status) {
        m_securityStatus = status;
        
        // Update status indicator based on status string
        if (status == "Active" || status == "Secure") {
            m_statusIndicator.SetStatus(StatusIndicator::Status::Secure);
        } else if (status == "Warning") {
            m_statusIndicator.SetStatus(StatusIndicator::Status::Warning);
        } else if (status == "Critical") {
            m_statusIndicator.SetStatus(StatusIndicator::Status::Critical);
        } else if (status == "Checking") {
            m_statusIndicator.SetStatus(StatusIndicator::Status::Checking);
        } else {
            m_statusIndicator.SetStatus(StatusIndicator::Status::Unknown);
        }
    }

    void HeaderComponent::Update(float deltaTime) {
        // Update status indicator
        m_statusIndicator.Update(deltaTime);
        
        // Update window controls
        m_windowControls.Update(deltaTime);
        
        // Fade in animation for title
        m_titleFadeIn = Animation::Transitions::FadeTransition(m_titleFadeIn, true, 2.0f, deltaTime);
    }

    // WindowControls implementation
    void WindowControls::Render() {
        const auto& theme = Theme::GetCurrentTheme();
        WindowManager& wm = WindowManager::GetInstance();
        
        ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(4, 0));
        ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(8, 4));
        
        // Window control buttons
        if (m_showPresets) {
            RenderPresetDropdown();
            ImGui::SameLine();
        }
        
        if (m_showMinimize) {
            bool minimizeClicked = false;
            RenderControlButton("_", "Minimize", minimizeClicked, theme.text);
            if (minimizeClicked) {
                wm.MinimizeWindow();
            }
            ImGui::SameLine();
        }
        
        if (m_showMaximize) {
            bool maximizeClicked = false;
            const char* maxIcon = wm.IsWindowMaximized() ? "❐" : "□";
            const char* maxTooltip = wm.IsWindowMaximized() ? "Restore" : "Maximize";
            RenderControlButton(maxIcon, maxTooltip, maximizeClicked, theme.text);
            if (maximizeClicked) {
                if (wm.IsWindowMaximized()) {
                    wm.RestoreWindow();
                } else {
                    wm.MaximizeWindow();
                }
            }
            ImGui::SameLine();
        }
        
        if (m_showClose) {
            bool closeClicked = false;
            RenderControlButton("×", "Close", closeClicked, theme.error);
            if (closeClicked) {
                m_shouldClose = true;
            }
        }
        
        ImGui::PopStyleVar(2);
    }
    
    void WindowControls::Update(float deltaTime) {
        // Update hover animations
        m_hoverAnimation = Animation::Transitions::SmoothStep(m_hoverAnimation, 0.0f, 5.0f, deltaTime);
    }
    
    void WindowControls::RenderControlButton(const char* icon, const char* tooltip, bool& clicked, ImVec4 color) {
        const auto& theme = Theme::GetCurrentTheme();
        
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0, 0, 0, 0));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, Theme::AdjustAlpha(color, 0.2f));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, Theme::AdjustAlpha(color, 0.4f));
        ImGui::PushStyleColor(ImGuiCol_Text, color);
        
        clicked = ImGui::Button(icon, ImVec2(24, 20));
        
        if (ImGui::IsItemHovered()) {
            ImGui::SetTooltip("%s", tooltip);
        }
        
        ImGui::PopStyleColor(4);
    }
    
    void WindowControls::RenderPresetDropdown() {
        const auto& theme = Theme::GetCurrentTheme();
        WindowManager& wm = WindowManager::GetInstance();
        
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0, 0, 0, 0));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, Theme::AdjustAlpha(theme.primary, 0.2f));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, Theme::AdjustAlpha(theme.primary, 0.4f));
        
        if (ImGui::Button("⚙", ImVec2(24, 20))) {
            ImGui::OpenPopup("WindowPresets");
        }
        
        if (ImGui::IsItemHovered()) {
            ImGui::SetTooltip("Window Presets");
        }
        
        if (ImGui::BeginPopup("WindowPresets")) {
            ImGui::Text("Window Management");
            ImGui::Separator();

            // Window state information
            ImVec2 currentPos = wm.GetWindowPosition();
            ImVec2 currentSize = wm.GetWindowSize();
            ImGui::Text("Position: %.0f, %.0f", currentPos.x, currentPos.y);
            ImGui::Text("Size: %.0f x %.0f", currentSize.x, currentSize.y);

            if (wm.IsWindowMaximized()) {
                ImGui::TextColored(theme.success, "Status: Maximized");
            } else if (wm.IsWindowMinimized()) {
                ImGui::TextColored(theme.warning, "Status: Minimized");
            } else {
                ImGui::TextColored(theme.text, "Status: Normal");
            }

            ImGui::Separator();

            // Quick actions
            if (ImGui::MenuItem("Center Window", "Ctrl+Alt+C")) {
                wm.CenterWindow();
            }

            if (ImGui::MenuItem("Maximize", "F11")) {
                if (wm.IsWindowMaximized()) {
                    wm.RestoreWindow();
                } else {
                    wm.MaximizeWindow();
                }
            }

            if (ImGui::MenuItem("Fit to Content")) {
                wm.FitWindowToContent(ImVec2(800, 600));
            }

            ImGui::Separator();

            // Window presets
            ImGui::Text("Presets");
            std::vector<std::string> presets = wm.GetWindowPositionPresets();

            if (presets.empty()) {
                ImGui::TextDisabled("No presets saved");
            } else {
                for (const std::string& preset : presets) {
                    if (ImGui::MenuItem(preset.c_str())) {
                        wm.ApplyWindowPositionPreset(preset);
                    }
                }
            }

            ImGui::Separator();

            // Save current as preset
            static char presetName[64] = "";
            ImGui::SetNextItemWidth(120);
            ImGui::InputText("##PresetName", presetName, sizeof(presetName));
            ImGui::SameLine();
            if (ImGui::Button("Save Preset")) {
                if (strlen(presetName) > 0) {
                    wm.SetWindowPositionPreset(presetName, wm.GetWindowPosition(), wm.GetWindowSize());
                    presetName[0] = '\0'; // Clear input
                }
            }

            ImGui::EndPopup();
        }
        
        ImGui::PopStyleColor(3);
    }

    // HeaderComponent implementation
    void HeaderComponent::Render() {
        const auto& theme = Theme::GetCurrentTheme();
        
        // Get window dimensions for responsive layout
        ImDrawList* drawList = ImGui::GetWindowDrawList();
        ImVec2 windowPos = ImGui::GetWindowPos();
        ImVec2 windowSize = ImGui::GetWindowSize();
        
        // Calculate responsive header height
        float headerHeight = (m_height > 50.0f) ? m_height : 50.0f;
        if (windowSize.x < 600.0f) {
            headerHeight *= 0.8f; // Smaller header on narrow windows
        }
        
        // Draw header background with subtle gradient
        ImVec4 headerBg = theme.surface;
        ImVec4 headerBgBottom = Theme::AdjustBrightness(theme.surface, 0.95f);
        
        drawList->AddRectFilledMultiColor(
            windowPos,
            ImVec2(windowPos.x + windowSize.x, windowPos.y + headerHeight),
            ImGui::ColorConvertFloat4ToU32(headerBg),
            ImGui::ColorConvertFloat4ToU32(headerBg),
            ImGui::ColorConvertFloat4ToU32(headerBgBottom),
            ImGui::ColorConvertFloat4ToU32(headerBgBottom)
        );
        
        // Draw header border if enabled
        if (m_showBorder) {
            drawList->AddLine(
                ImVec2(windowPos.x, windowPos.y + headerHeight),
                ImVec2(windowPos.x + windowSize.x, windowPos.y + headerHeight),
                ImGui::ColorConvertFloat4ToU32(theme.border),
                1.0f
            );
        }

        // Calculate content positioning
        float contentPadding = 16.0f;
        float verticalCenter = headerHeight * 0.5f;
        
        // Title section with fade-in animation
        ImGui::SetCursorPos(ImVec2(contentPadding, verticalCenter - 12.0f));
        
        // Apply title styling with animation
        ImVec4 titleColor = Theme::WithAlpha(theme.text, m_titleFadeIn);
        ImGui::PushStyleColor(ImGuiCol_Text, titleColor);
        
        // Main title
        ImGui::Text("%s", m_title.c_str());
        
        // Subtitle if present
        if (!m_subtitle.empty()) {
            ImGui::SetCursorPos(ImVec2(contentPadding, verticalCenter + 2.0f));
            ImVec4 subtitleColor = Theme::WithAlpha(theme.textSecondary, m_titleFadeIn * 0.8f);
            ImGui::PushStyleColor(ImGuiCol_Text, subtitleColor);
            ImGui::Text("%s", m_subtitle.c_str());
            ImGui::PopStyleColor();
        }
        
        ImGui::PopStyleColor();
        
        // Window controls section (top-right)
        if (m_showWindowControls) {
            float controlsWidth = 100.0f; // Approximate width for controls
            ImGui::SetCursorPos(ImVec2(windowSize.x - controlsWidth - contentPadding, 8.0f));
            m_windowControls.Render();
        }
        
        // Security status section (right-aligned)
        float statusSectionWidth = 180.0f;
        float controlsOffset = m_showWindowControls ? 110.0f : 0.0f;
        
        // Responsive adjustment for narrow windows
        if (windowSize.x < 600.0f) {
            float maxWidth = windowSize.x * 0.4f;
            statusSectionWidth = (statusSectionWidth < maxWidth) ? statusSectionWidth : maxWidth;
            controlsOffset = m_showWindowControls ? 60.0f : 0.0f;
        }
        
        ImGui::SetCursorPos(ImVec2(windowSize.x - statusSectionWidth - contentPadding - controlsOffset, verticalCenter - 8.0f));
        
        // Status label
        ImGui::PushStyleColor(ImGuiCol_Text, theme.textSecondary);
        ImGui::Text("Status:");
        ImGui::PopStyleColor();
        
        // Status indicator
        ImGui::SameLine();
        ImGui::SetCursorPosX(windowSize.x - statusSectionWidth + 40.0f);
        m_statusIndicator.Render();
        
        // Add spacing after header
        ImGui::SetCursorPosY(headerHeight + 10.0f);
        
        // Add subtle shadow effect below header
        ImVec2 shadowStart = ImVec2(windowPos.x, windowPos.y + headerHeight);
        ImVec2 shadowEnd = ImVec2(windowPos.x + windowSize.x, windowPos.y + headerHeight + 4.0f);
        
        for (int i = 0; i < 4; i++) {
            float alpha = (4 - i) * 0.05f;
            ImVec4 shadowColor = Theme::WithAlpha(ImVec4(0, 0, 0, 1), alpha);
            drawList->AddLine(
                ImVec2(shadowStart.x, shadowStart.y + i),
                ImVec2(shadowEnd.x, shadowStart.y + i),
                ImGui::ColorConvertFloat4ToU32(shadowColor),
                1.0f
            );
        }
    }

    // FooterComponent implementation
    void FooterComponent::Render() {
        const auto& theme = Theme::GetCurrentTheme();
        
        // Get window dimensions for responsive layout
        ImDrawList* drawList = ImGui::GetWindowDrawList();
        ImVec2 windowPos = ImGui::GetWindowPos();
        ImVec2 windowSize = ImGui::GetWindowSize();
        
        // Calculate footer position (bottom of window)
        float footerY = windowPos.y + windowSize.y - m_height;
        
        // Draw footer background with subtle gradient
        ImVec4 footerBg = Theme::AdjustBrightness(theme.surface, 0.95f);
        ImVec4 footerBgTop = Theme::AdjustBrightness(theme.surface, 0.98f);
        
        drawList->AddRectFilledMultiColor(
            ImVec2(windowPos.x, footerY),
            ImVec2(windowPos.x + windowSize.x, footerY + m_height),
            ImGui::ColorConvertFloat4ToU32(footerBgTop),
            ImGui::ColorConvertFloat4ToU32(footerBgTop),
            ImGui::ColorConvertFloat4ToU32(footerBg),
            ImGui::ColorConvertFloat4ToU32(footerBg)
        );
        
        // Draw top border with subtle glow
        drawList->AddLine(
            ImVec2(windowPos.x, footerY),
            ImVec2(windowPos.x + windowSize.x, footerY),
            ImGui::ColorConvertFloat4ToU32(theme.border),
            1.0f
        );
        
        // Add subtle highlight line
        drawList->AddLine(
            ImVec2(windowPos.x, footerY + 1),
            ImVec2(windowPos.x + windowSize.x, footerY + 1),
            ImGui::ColorConvertFloat4ToU32(Theme::WithAlpha(theme.primary, 0.1f)),
            1.0f
        );
        
        // Set cursor position for footer content
        ImGui::SetCursorPos(ImVec2(12.0f, windowSize.y - m_height + 6.0f));
        
        // Choose rendering mode based on window size
        if (IsCompactMode()) {
            RenderCompactFooter();
        } else {
            RenderDetailedFooter();
        }
    }

    void FooterComponent::RenderCompactFooter() {
        const auto& theme = Theme::GetCurrentTheme();
        ImVec2 windowSize = ImGui::GetWindowSize();
        
        // Compact layout: Version | Status | Time
        
        // Version info (left side)
        ImGui::PushStyleColor(ImGuiCol_Text, theme.textSecondary);
        ImGui::Text("%s", m_version.c_str());
        ImGui::PopStyleColor();
        
        // Status info (center)
        float statusWidth = ImGui::CalcTextSize(m_status.c_str()).x;
        ImGui::SameLine(windowSize.x * 0.5f - statusWidth * 0.5f);
        
        // Status with color coding
        ImVec4 statusColor = theme.text;
        if (m_status.find("Error") != std::string::npos || m_status.find("Critical") != std::string::npos) {
            statusColor = theme.error;
        } else if (m_status.find("Warning") != std::string::npos) {
            statusColor = theme.warning;
        } else if (m_status.find("Ready") != std::string::npos || m_status.find("Active") != std::string::npos) {
            statusColor = theme.success;
        }
        
        ImGui::PushStyleColor(ImGuiCol_Text, statusColor);
        ImGui::Text("%s", m_status.c_str());
        ImGui::PopStyleColor();
        
        // Last check time (right side)
        std::string timeText = FormatTimeAgo(m_lastCheckTime);
        float timeTextWidth = ImGui::CalcTextSize(timeText.c_str()).x;
        ImGui::SameLine(windowSize.x - timeTextWidth - 12.0f);
        ImGui::PushStyleColor(ImGuiCol_Text, Theme::WithAlpha(theme.textSecondary, 0.8f + 0.2f * m_fadeAnimation));
        ImGui::Text("%s", timeText.c_str());
        ImGui::PopStyleColor();
    }

    void FooterComponent::RenderDetailedFooter() {
        const auto& theme = Theme::GetCurrentTheme();
        ImVec2 windowSize = ImGui::GetWindowSize();
        
        // Detailed layout with multiple sections
        float sectionWidth = windowSize.x / 5.0f; // 5 sections
        float currentX = 12.0f;
        
        // Section 1: Version and Build Info
        ImGui::SetCursorPosX(currentX);
        ImGui::PushStyleColor(ImGuiCol_Text, theme.textSecondary);
        ImGui::Text("%s", m_version.c_str());
        if (m_showDetailedInfo && !m_buildInfo.empty()) {
            ImGui::SameLine();
            ImGui::Text(" (%s)", m_buildInfo.c_str());
        }
        ImGui::PopStyleColor();
        
        // Section 2: Connection Status
        currentX += sectionWidth;
        ImGui::SameLine(currentX);
        
        // Connection status with icon
        const char* connectionIcon = "[NET]";
        if (m_connectionStatus.find("Disconnected") != std::string::npos) {
            connectionIcon = "[OFF]";
        } else if (m_connectionStatus.find("Error") != std::string::npos) {
            connectionIcon = "[ERR]";
        }
        
        ImGui::PushStyleColor(ImGuiCol_Text, theme.textSecondary);
        ImGui::Text("%s %s", connectionIcon, m_connectionStatus.c_str());
        ImGui::PopStyleColor();
        
        // Section 3: System Status
        currentX += sectionWidth;
        ImGui::SameLine(currentX);
        
        // Status with color coding and icon
        const char* statusIcon = "[i]";
        ImVec4 statusColor = theme.text;
        if (m_status.find("Error") != std::string::npos || m_status.find("Critical") != std::string::npos) {
            statusColor = theme.error;
            statusIcon = "[X]";
        } else if (m_status.find("Warning") != std::string::npos) {
            statusColor = theme.warning;
            statusIcon = "[!]";
        } else if (m_status.find("Ready") != std::string::npos || m_status.find("Active") != std::string::npos) {
            statusColor = theme.success;
            statusIcon = "[OK]";
        }
        
        ImGui::PushStyleColor(ImGuiCol_Text, statusColor);
        ImGui::Text("%s %s", statusIcon, m_status.c_str());
        ImGui::PopStyleColor();
        
        // Section 4: Performance Info (if detailed info is enabled)
        if (m_showDetailedInfo) {
            currentX += sectionWidth;
            ImGui::SameLine(currentX);
            
            ImGui::PushStyleColor(ImGuiCol_Text, theme.textSecondary);
            if (m_memoryUsage > 0.0f) {
                ImGui::Text("RAM: %s", FormatMemoryUsage(m_memoryUsage).c_str());
            }
            if (m_cpuUsage > 0.0f) {
                ImGui::SameLine();
                ImGui::Text(" CPU: %.1f%%", m_cpuUsage);
            }
            ImGui::PopStyleColor();
        }
        
        // Section 5: Last Check Time (right side)
        std::string timeText = FormatTimeAgo(m_lastCheckTime);
        float timeTextWidth = ImGui::CalcTextSize(timeText.c_str()).x;
        ImGui::SameLine(windowSize.x - timeTextWidth - 12.0f);
        ImGui::PushStyleColor(ImGuiCol_Text, Theme::WithAlpha(theme.textSecondary, 0.8f + 0.2f * m_fadeAnimation));
        ImGui::Text("%s", timeText.c_str());
        ImGui::PopStyleColor();
    }

    std::string FooterComponent::FormatTimeAgo(const std::chrono::steady_clock::time_point& time) const {
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - time);
        
        if (duration.count() < 60) {
            return "Last check: " + std::to_string(duration.count()) + "s ago";
        } else if (duration.count() < 3600) {
            return "Last check: " + std::to_string(duration.count() / 60) + "m ago";
        } else if (duration.count() < 86400) {
            return "Last check: " + std::to_string(duration.count() / 3600) + "h ago";
        } else {
            return "Last check: " + std::to_string(duration.count() / 86400) + "d ago";
        }
    }

    std::string FooterComponent::FormatMemoryUsage(float memoryMB) const {
        if (memoryMB < 1024.0f) {
            return std::to_string((int)memoryMB) + "MB";
        } else {
            return std::to_string((int)(memoryMB / 1024.0f * 10) / 10.0f) + "GB";
        }
    }

    bool FooterComponent::IsCompactMode() const {
        ImVec2 windowSize = ImGui::GetWindowSize();
        return windowSize.x < 800.0f; // Switch to compact mode for narrow windows
    }

    void FooterComponent::Update(float deltaTime) {
        // Update fade animation
        m_fadeAnimation = Animation::Transitions::PulseAnimation(
            std::chrono::duration<float>(std::chrono::steady_clock::now().time_since_epoch()).count(),
            4.0f, 0.3f
        );
    }

    // MainWindowLayout implementation
    void MainWindowLayout::Render() {
        const auto& theme = Theme::GetCurrentTheme();
        
        // Update responsive layout based on current window size
        UpdateResponsiveLayout();
        
        // Set window properties
        ImGui::SetNextWindowPos(m_windowPosition, ImGuiCond_FirstUseEver);
        ImGui::SetNextWindowSize(m_windowSize, ImGuiCond_FirstUseEver);
        
        // Window flags for main window
        ImGuiWindowFlags windowFlags = ImGuiWindowFlags_NoCollapse;
        if (m_isCompactMode) {
            windowFlags |= ImGuiWindowFlags_NoTitleBar;
        }
        
        bool isWindowOpen = true;
        if (ImGui::Begin("Nebula Loader - Security Management", &isWindowOpen, windowFlags)) {
            // Update actual window size and position
            m_windowSize = ImGui::GetWindowSize();
            m_windowPosition = ImGui::GetWindowPos();
            
            // Render header
            if (m_header) {
                m_header->SetHeight(m_headerHeight);
                m_header->Render();
            }
            
            // Render main content area
            RenderContent();
            
            // Render footer
            if (m_footer) {
                m_footer->SetHeight(m_footerHeight);
                m_footer->Render();
            }
        }
        ImGui::End();
        
        // Handle window close
        if (!isWindowOpen) {
            // Signal application to close
            // This would typically set a global flag or call a callback
        }
    }

    void MainWindowLayout::Update(float deltaTime) {
        // Update layout animation
        m_layoutAnimation += deltaTime;
        
        // Handle window events and state management
        HandleWindowEvents();
        
        // Update state save timer
        if (m_windowStateChanged) {
            m_stateSaveTimer += deltaTime;
            if (m_stateSaveTimer >= STATE_SAVE_DELAY) {
                SaveWindowState();
                m_stateSaveTimer = 0.0f;
            }
        }
        
        // Update all child components
        if (m_header) {
            m_header->Update(deltaTime);
        }
        
        if (m_footer) {
            m_footer->Update(deltaTime);
            // Update footer with current time
            m_footer->SetLastCheckTime(std::chrono::steady_clock::now());
        }
        
        if (m_securityDashboard) {
            m_securityDashboard->Update(deltaTime);
        }
    }

    void MainWindowLayout::UpdateResponsiveLayout() {
        // Determine if we should use compact mode
        bool shouldBeCompact = IsSmallScreen();
        
        if (shouldBeCompact != m_isCompactMode) {
            m_isCompactMode = shouldBeCompact;
            
            // Adjust layout parameters for compact mode
            if (m_isCompactMode) {
                m_headerHeight = 45.0f;
                m_footerHeight = 25.0f;
                m_contentPadding = 8.0f;
            } else {
                m_headerHeight = 60.0f;
                m_footerHeight = 30.0f;
                m_contentPadding = 16.0f;
            }
        }
        
        // Adjust window size constraints
        if (m_windowSize.x < 320.0f) {
            m_windowSize.x = 320.0f; // Minimum width
        }
        if (m_windowSize.y < 240.0f) {
            m_windowSize.y = 240.0f; // Minimum height
        }
    }

    void MainWindowLayout::RenderContent() {
        const auto& theme = Theme::GetCurrentTheme();
        
        // Calculate content area dimensions
        float contentHeight = GetContentHeight();
        float contentY = m_headerHeight + 8.0f;
        
        // Set up content area
        ImGui::SetCursorPosY(contentY);
        
        // Create scrollable content area
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(m_contentPadding, m_contentPadding));
        
        if (ImGui::BeginChild("MainContent", ImVec2(0, contentHeight), false, ImGuiWindowFlags_AlwaysVerticalScrollbar)) {
            // Render security dashboard
            if (m_securityDashboard) {
                m_securityDashboard->Render();
            }
            
            // Add some spacing at the bottom
            ImGui::Spacing();
            ImGui::Spacing();
            
            // Demo content for testing layout
            if (IsSmallScreen()) {
                // Compact layout for small screens
                ImGui::Text("Compact Mode Active");
                ImGui::Text("Screen Width: %.0f px", m_windowSize.x);
            } else {
                // Full layout for larger screens
                ImGui::Text("Full Layout Mode");
                ImGui::Text("Window Size: %.0f x %.0f px", m_windowSize.x, m_windowSize.y);
                
                // Additional content for larger screens
                ImGui::Separator();
                ImGui::Text("Additional Controls:");
                
                static ModernButton settingsBtn("settings_btn", "Settings");
                static ModernButton aboutBtn("about_btn", "About");
                
                settingsBtn.SetStyle(ModernButton::Style::Secondary);
                aboutBtn.SetStyle(ModernButton::Style::Secondary);
                
                if (settingsBtn.Render(ImVec2(100, 30))) {
                    // Settings button clicked
                }
                
                ImGui::SameLine();
                if (aboutBtn.Render(ImVec2(100, 30))) {
                    // About button clicked
                }
            }
        }
        ImGui::EndChild();
        
        ImGui::PopStyleVar(); // WindowPadding
    }

    float MainWindowLayout::GetContentHeight() const {
        // Calculate available content height
        float totalHeight = m_windowSize.y;
        float usedHeight = m_headerHeight + m_footerHeight + 16.0f; // 16px for spacing
        return totalHeight - usedHeight;
    }

    void MainWindowLayout::SaveWindowState() {
        // Update window state with current values
        m_windowState.position = m_windowPosition;
        m_windowState.size = m_windowSize;
        
        // Use the WindowManager from ui.hpp for persistence
        WindowManager& manager = WindowManager::GetInstance();
        manager.SetWindowPosition(m_windowPosition);
        manager.SetWindowSize(m_windowSize);
        manager.SaveWindowState();
        
        m_windowStateChanged = false;
    }

    void MainWindowLayout::LoadWindowState() {
        // Load window state using WindowManager
        WindowManager& manager = WindowManager::GetInstance();
        manager.LoadWindowState();
        
        // Apply loaded state
        m_windowSize = manager.GetWindowSize();
        m_windowPosition = manager.GetWindowPosition();
        
        // Update internal state
        m_windowState.position = m_windowPosition;
        m_windowState.size = m_windowSize;
    }

    void MainWindowLayout::SetWindowConstraints(ImVec2 minSize, ImVec2 maxSize) {
        WindowManager& manager = WindowManager::GetInstance();
        manager.SetMinWindowSize(minSize);
        manager.SetMaxWindowSize(maxSize);
    }

    void MainWindowLayout::HandleWindowEvents() {
        // Check for window size changes
        if (m_windowSize.x != m_lastFrameSize.x || m_windowSize.y != m_lastFrameSize.y) {
            WindowManager& manager = WindowManager::GetInstance();
            manager.SetWindowSize(m_windowSize);
            m_windowStateChanged = true;
            m_stateSaveTimer = 0.0f;
            m_lastFrameSize = m_windowSize;
        }
        
        // Check for window position changes
        if (m_windowPosition.x != m_lastFramePosition.x || m_windowPosition.y != m_lastFramePosition.y) {
            WindowManager& manager = WindowManager::GetInstance();
            manager.SetWindowPosition(m_windowPosition);
            m_windowStateChanged = true;
            m_stateSaveTimer = 0.0f;
            m_lastFramePosition = m_windowPosition;
        }
        
        // Apply window constraints using WindowManager
        WindowManager& manager = WindowManager::GetInstance();
        manager.EnforceWindowConstraints();
        
        // Get constrained values back
        ImVec2 constrainedSize = manager.GetWindowSize();
        ImVec2 constrainedPosition = manager.GetWindowPosition();
        
        if (constrainedSize.x != m_windowSize.x || constrainedSize.y != m_windowSize.y) {
            m_windowSize = constrainedSize;
            m_windowStateChanged = true;
        }
        
        if (constrainedPosition.x != m_windowPosition.x || constrainedPosition.y != m_windowPosition.y) {
            m_windowPosition = constrainedPosition;
            m_windowStateChanged = true;
        }
    }

    bool MainWindowLayout::IsWindowStateChanged() const {
        return m_windowStateChanged;
    }

    // SecurityCheckCard implementation
    void SecurityCheckCard::Render() {
        const auto& theme = Theme::GetCurrentTheme();
        
        // Get current time for animations
        static auto startTime = std::chrono::steady_clock::now();
        auto currentTime = std::chrono::steady_clock::now();
        float deltaTime = std::chrono::duration<float>(currentTime - startTime).count();
        startTime = currentTime;

        // Handle hover animation
        bool isHovered = ImGui::IsItemHovered();
        m_hoverAnimation = Animation::Transitions::SmoothHover(m_hoverAnimation, isHovered, 6.0f, deltaTime);

        // Handle expand animation
        float targetExpand = m_check.isExpanded ? 1.0f : 0.0f;
        m_expandAnimation = Animation::GetAnimationSystem().SmoothStep(m_expandAnimation, targetExpand, 8.0f, deltaTime);

        // Calculate card colors
        ImVec4 cardBg = Theme::BlendColors(theme.surface, theme.surfaceHover, m_hoverAnimation * 0.5f);
        ImVec4 borderColor = Theme::BlendColors(theme.border, theme.borderHover, m_hoverAnimation);

        // Get status color
        ImVec4 statusColor;
        switch (m_check.status) {
            case StatusIndicator::Status::Secure:
                statusColor = theme.success;
                break;
            case StatusIndicator::Status::Warning:
                statusColor = theme.warning;
                break;
            case StatusIndicator::Status::Critical:
                statusColor = theme.error;
                break;
            case StatusIndicator::Status::Checking:
                statusColor = theme.warning;
                break;
            default:
                statusColor = theme.textSecondary;
                break;
        }

        // Begin card container
        ImGui::PushStyleColor(ImGuiCol_ChildBg, cardBg);
        ImGui::PushStyleColor(ImGuiCol_Border, borderColor);
        ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 1.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, theme.cornerRadius);
        
        float cardHeight = 60.0f + (m_expandAnimation * 40.0f); // Base height + expanded content
        
        if (ImGui::BeginChild(m_id.c_str(), ImVec2(0, cardHeight), true)) {
            // Card header with status indicator and name
            ImGui::SetCursorPosY(8.0f);
            
            // Status icon with better visual representation
            const char* statusIcon = "";
            ImVec4 iconColor = statusColor;
            switch (m_check.status) {
                case StatusIndicator::Status::Secure:
                    statusIcon = "[OK]";
                    break;
                case StatusIndicator::Status::Warning:
                    statusIcon = "[!]";
                    break;
                case StatusIndicator::Status::Critical:
                    statusIcon = "[X]";
                    break;
                case StatusIndicator::Status::Checking:
                    statusIcon = "[~]";
                    break;
                default:
                    statusIcon = "[?]";
                    break;
            }
            
            // Draw status icon
            ImGui::PushStyleColor(ImGuiCol_Text, iconColor);
            ImGui::Text("%s", statusIcon);
            ImGui::PopStyleColor();
            
            // Status indicator (small dot)
            ImGui::SameLine();
            StatusIndicator indicator(m_id + "_status");
            indicator.SetStatus(m_check.status);
            indicator.SetSize(4.0f);
            indicator.SetShowText(false);
            indicator.SetAnimated(m_check.status == StatusIndicator::Status::Checking);
            indicator.Render();
            
            // Check name
            ImGui::SameLine();
            ImGui::PushStyleColor(ImGuiCol_Text, theme.text);
            ImGui::Text("%s", m_check.name.c_str());
            ImGui::PopStyleColor();
            
            // Expand/collapse indicator (right side)
            const char* expandIcon = m_check.isExpanded ? "▼" : "▶";
            float expandIconWidth = ImGui::CalcTextSize(expandIcon).x;
            ImGui::SameLine(ImGui::GetWindowWidth() - expandIconWidth - 80.0f);
            ImGui::PushStyleColor(ImGuiCol_Text, Theme::WithAlpha(theme.textSecondary, 0.7f + 0.3f * m_hoverAnimation));
            ImGui::Text("%s", expandIcon);
            ImGui::PopStyleColor();
            
            // Status text (right-aligned)
            const char* statusText = "";
            switch (m_check.status) {
                case StatusIndicator::Status::Secure: statusText = "SECURE"; break;
                case StatusIndicator::Status::Warning: statusText = "WARNING"; break;
                case StatusIndicator::Status::Critical: statusText = "CRITICAL"; break;
                case StatusIndicator::Status::Checking: statusText = "CHECKING"; break;
                default: statusText = "UNKNOWN"; break;
            }
            
            float statusTextWidth = ImGui::CalcTextSize(statusText).x;
            ImGui::SameLine(ImGui::GetWindowWidth() - statusTextWidth - 16.0f);
            ImGui::PushStyleColor(ImGuiCol_Text, statusColor);
            ImGui::Text("%s", statusText);
            ImGui::PopStyleColor();
            
            // Description
            ImGui::SetCursorPosY(28.0f);
            ImGui::PushStyleColor(ImGuiCol_Text, theme.textSecondary);
            ImGui::Text("%s", m_check.description.c_str());
            ImGui::PopStyleColor();
            
            // Progress bar for checking status
            if (m_check.status == StatusIndicator::Status::Checking && m_check.progress > 0.0f) {
                ImGui::SetCursorPosY(45.0f);
                ImGui::PushStyleColor(ImGuiCol_PlotHistogram, statusColor);
                ImGui::ProgressBar(m_check.progress, ImVec2(-1, 4.0f), "");
                ImGui::PopStyleColor();
            }
            
            // Expanded details (animated)
            if (m_expandAnimation > 0.01f) {
                ImGui::SetCursorPosY(60.0f);
                ImGui::PushStyleVar(ImGuiStyleVar_Alpha, m_expandAnimation);
                
                // Separator line with gradient effect
                ImDrawList* drawList = ImGui::GetWindowDrawList();
                ImVec2 separatorStart = ImGui::GetCursorScreenPos();
                ImVec2 separatorEnd = ImVec2(separatorStart.x + ImGui::GetWindowWidth() - 32.0f, separatorStart.y);
                drawList->AddLine(separatorStart, separatorEnd, ImGui::ColorConvertFloat4ToU32(theme.border), 1.0f);
                
                ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 8.0f);
                
                // Details section header
                ImGui::PushStyleColor(ImGuiCol_Text, theme.text);
                ImGui::Text("Details:");
                ImGui::PopStyleColor();
                
                // Details text with better formatting
                if (!m_check.details.empty()) {
                    ImGui::Indent(16.0f);
                    ImGui::PushStyleColor(ImGuiCol_Text, theme.textSecondary);
                    ImGui::PushTextWrapPos(ImGui::GetWindowWidth() - 48.0f);
                    ImGui::TextWrapped("%s", m_check.details.c_str());
                    ImGui::PopTextWrapPos();
                    ImGui::PopStyleColor();
                    ImGui::Unindent(16.0f);
                } else {
                    ImGui::Indent(16.0f);
                    ImGui::PushStyleColor(ImGuiCol_Text, Theme::WithAlpha(theme.textSecondary, 0.6f));
                    ImGui::Text("No additional details available.");
                    ImGui::PopStyleColor();
                    ImGui::Unindent(16.0f);
                }
                
                ImGui::PopStyleVar(); // Alpha
            }
        }
        ImGui::EndChild();
        
        // Handle click to expand/collapse
        if (ImGui::IsItemClicked()) {
            m_check.isExpanded = !m_check.isExpanded;
        }
        
        ImGui::PopStyleVar(2); // ChildRounding, ChildBorderSize
        ImGui::PopStyleColor(2); // ChildBg, Border
    }

    void SecurityCheckCard::Update(float deltaTime) {
        // Update animations handled in Render()
    }

    // SecurityDashboard implementation
    void SecurityDashboard::Render() {
        const auto& theme = Theme::GetCurrentTheme();
        
        // Dashboard header
        ImGui::PushStyleColor(ImGuiCol_Text, theme.text);
        ImGui::Text("Security Dashboard");
        ImGui::PopStyleColor();
        
        ImGui::Spacing();
        
        // Enhanced security status summary with recommendations
        if (m_statusSummary) {
            // Update status summary with current data
            m_statusSummary->SetOverallStatus(m_overallStatus);
            
            // Calculate security level
            float securityLevel = 0.0f;
            int secureCount = 0, totalCount = 0;
            for (const auto& check : m_securityChecks) {
                totalCount++;
                if (check.status == StatusIndicator::Status::Secure) {
                    secureCount++;
                }
            }
            if (totalCount > 0) {
                securityLevel = (float)secureCount / totalCount;
            }
            m_statusSummary->SetSecurityLevel(securityLevel);
            
            // Generate and set recommendations
            auto recommendations = m_statusSummary->GenerateRecommendations(m_securityChecks);
            m_statusSummary->SetRecommendations(recommendations);
            
            // Render the enhanced status summary
            m_statusSummary->Render();
        }
        
        ImGui::Spacing();
        
        // Security checks header
        ImGui::PushStyleColor(ImGuiCol_Text, theme.text);
        ImGui::Text("Security Checks (%zu)", m_securityChecks.size());
        ImGui::PopStyleColor();
        
        ImGui::Spacing();
        
        // Render security check cards
        for (auto& card : m_checkCards) {
            card->Render();
            ImGui::Spacing();
        }
        
        // Show message if no checks available
        if (m_securityChecks.empty()) {
            ImGui::PushStyleColor(ImGuiCol_Text, theme.textSecondary);
            ImGui::Text("No security checks configured.");
            ImGui::Text("Security checks will appear here when the system is initialized.");
            ImGui::PopStyleColor();
        }
    }

    void SecurityDashboard::Update(float deltaTime) {
        // Update animation progress
        m_animationProgress += deltaTime;
        
        // Update all check cards
        for (auto& card : m_checkCards) {
            card->Update(deltaTime);
        }
        
        // Update status summary
        if (m_statusSummary) {
            m_statusSummary->Update(deltaTime);
        }
        
        // Update overall status based on individual checks
        StatusIndicator::Status calculatedStatus = CalculateOverallStatus();
        if (calculatedStatus != m_overallStatus) {
            m_overallStatus = calculatedStatus;
        }
        
        // Track last check time
        m_lastCheckTime += deltaTime;
    }

    void SecurityDashboard::UpdateSecurityChecks(const std::vector<SecurityCheck>& checks) {
        m_securityChecks = checks;
        CreateCheckCards();
    }

    void SecurityDashboard::AddSecurityCheck(const SecurityCheck& check) {
        m_securityChecks.push_back(check);
        CreateCheckCards();
    }

    void SecurityDashboard::UpdateSecurityCheck(const std::string& name, StatusIndicator::Status status, const std::string& details) {
        for (auto& check : m_securityChecks) {
            if (check.name == name) {
                check.status = status;
                check.lastUpdate = std::chrono::steady_clock::now();
                if (!details.empty()) {
                    check.details = details;
                }
                break;
            }
        }
        
        // Update corresponding card
        for (auto& card : m_checkCards) {
            if (card->GetCheck().name == name) {
                SecurityCheck updatedCheck = card->GetCheck();
                updatedCheck.status = status;
                updatedCheck.lastUpdate = std::chrono::steady_clock::now();
                if (!details.empty()) {
                    updatedCheck.details = details;
                }
                card->SetCheck(updatedCheck);
                break;
            }
        }
    }

    void SecurityDashboard::SetProgress(const std::string& name, float progress) {
        for (auto& check : m_securityChecks) {
            if (check.name == name) {
                check.progress = (progress < 0.0f) ? 0.0f : (progress > 1.0f) ? 1.0f : progress;
                break;
            }
        }
        
        // Update corresponding card
        for (auto& card : m_checkCards) {
            if (card->GetCheck().name == name) {
                SecurityCheck updatedCheck = card->GetCheck();
                updatedCheck.progress = (progress < 0.0f) ? 0.0f : (progress > 1.0f) ? 1.0f : progress;
                card->SetCheck(updatedCheck);
                break;
            }
        }
    }

    void SecurityDashboard::CreateCheckCards() {
        m_checkCards.clear();
        for (size_t i = 0; i < m_securityChecks.size(); ++i) {
            std::string cardId = "security_card_" + std::to_string(i);
            m_checkCards.push_back(std::make_unique<SecurityCheckCard>(cardId, m_securityChecks[i]));
        }
    }

    StatusIndicator::Status SecurityDashboard::CalculateOverallStatus() const {
        if (m_securityChecks.empty()) {
            return StatusIndicator::Status::Unknown;
        }
        
        bool hasChecking = false;
        bool hasCritical = false;
        bool hasWarning = false;
        int secureCount = 0;
        
        for (const auto& check : m_securityChecks) {
            switch (check.status) {
                case StatusIndicator::Status::Critical:
                    hasCritical = true;
                    break;
                case StatusIndicator::Status::Warning:
                    hasWarning = true;
                    break;
                case StatusIndicator::Status::Checking:
                    hasChecking = true;
                    break;
                case StatusIndicator::Status::Secure:
                    secureCount++;
                    break;
                default:
                    break;
            }
        }
        
        // Priority: Critical > Warning > Checking > Secure > Unknown
        if (hasCritical) {
            return StatusIndicator::Status::Critical;
        } else if (hasWarning) {
            return StatusIndicator::Status::Warning;
        } else if (hasChecking) {
            return StatusIndicator::Status::Checking;
        } else if (secureCount == (int)m_securityChecks.size()) {
            return StatusIndicator::Status::Secure;
        } else {
            return StatusIndicator::Status::Unknown;
        }
    }

    ImVec4 SecurityDashboard::GetStatusColor(StatusIndicator::Status status) const {
        const auto& theme = Theme::GetCurrentTheme();
        switch (status) {
            case StatusIndicator::Status::Secure: return theme.success;
            case StatusIndicator::Status::Warning: return theme.warning;
            case StatusIndicator::Status::Critical: return theme.error;
            case StatusIndicator::Status::Checking: return theme.warning;
            default: return theme.textSecondary;
        }
    }

    const char* SecurityDashboard::GetStatusText(StatusIndicator::Status status) const {
        switch (status) {
            case StatusIndicator::Status::Secure: return "All security checks passed";
            case StatusIndicator::Status::Warning: return "Some security warnings detected";
            case StatusIndicator::Status::Critical: return "Critical security issues found";
            case StatusIndicator::Status::Checking: return "Security checks in progress...";
            default: return "Security status unknown";
        }
    }

    const char* SecurityDashboard::GetStatusIcon(StatusIndicator::Status status) const {
        switch (status) {
            case StatusIndicator::Status::Secure: return "✓";
            case StatusIndicator::Status::Warning: return "!";
            case StatusIndicator::Status::Critical: return "✗";
            case StatusIndicator::Status::Checking: return "~";
            default: return "?";
        }
    }

    // SecurityStatusSummary implementation
    void SecurityStatusSummary::Render() {
        const auto& theme = Theme::GetCurrentTheme();
        
        // Enhanced overall status section
        ImGui::PushStyleColor(ImGuiCol_ChildBg, theme.surface);
        ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, theme.cornerRadius);
        ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 1.0f);
        ImGui::PushStyleColor(ImGuiCol_Border, theme.border);
        
        if (ImGui::BeginChild("enhanced_overall_status", ImVec2(0, 120.0f), true)) {
            // Header with security shield icon
            ImGui::SetCursorPos(ImVec2(16.0f, 16.0f));
            ImGui::PushStyleColor(ImGuiCol_Text, theme.primary);
            ImGui::Text("[SEC]");
            ImGui::PopStyleColor();
            
            ImGui::SameLine();
            ImGui::SetCursorPosY(20.0f);
            ImGui::PushStyleColor(ImGuiCol_Text, theme.text);
            ImGui::Text("Security Status Overview");
            ImGui::PopStyleColor();
            
            // Overall status indicator (larger)
            StatusIndicator overallIndicator("enhanced_overall_status_indicator");
            overallIndicator.SetStatus(m_overallStatus);
            overallIndicator.SetSize(10.0f);
            overallIndicator.SetAnimated(m_overallStatus == StatusIndicator::Status::Checking);
            
            ImGui::SetCursorPos(ImVec2(16.0f, 45.0f));
            overallIndicator.Render();
            
            // Status description with better formatting
            ImGui::SameLine();
            ImGui::SetCursorPosY(48.0f);
            ImVec4 statusColor = GetSeverityColor(m_overallStatus);
            ImGui::PushStyleColor(ImGuiCol_Text, statusColor);
            
            const char* statusText = "";
            const char* statusDescription = "";
            switch (m_overallStatus) {
                case StatusIndicator::Status::Secure:
                    statusText = "SYSTEM SECURE";
                    statusDescription = "All security checks passed successfully";
                    break;
                case StatusIndicator::Status::Warning:
                    statusText = "SECURITY WARNINGS";
                    statusDescription = "Some security concerns require attention";
                    break;
                case StatusIndicator::Status::Critical:
                    statusText = "CRITICAL THREATS";
                    statusDescription = "Immediate security action required";
                    break;
                case StatusIndicator::Status::Checking:
                    statusText = "SECURITY SCAN";
                    statusDescription = "Security checks in progress...";
                    break;
                default:
                    statusText = "STATUS UNKNOWN";
                    statusDescription = "Security status not determined";
                    break;
            }
            
            ImGui::Text("%s", statusText);
            ImGui::PopStyleColor();
            
            ImGui::SetCursorPos(ImVec2(16.0f, 68.0f));
            ImGui::PushStyleColor(ImGuiCol_Text, theme.textSecondary);
            ImGui::Text("%s", statusDescription);
            ImGui::PopStyleColor();
            
            // Security level progress bar (right side)
            float windowWidth = ImGui::GetWindowWidth();
            ImGui::SetCursorPos(ImVec2(windowWidth - 180.0f, 45.0f));
            ImGui::PushStyleColor(ImGuiCol_Text, theme.textSecondary);
            ImGui::Text("Security Level");
            ImGui::PopStyleColor();
            
            ImGui::SetCursorPos(ImVec2(windowWidth - 180.0f, 65.0f));
            
            // Animated security level bar
            ImVec4 levelColor = Theme::BlendColors(theme.error, theme.success, m_securityLevel);
            ImGui::PushStyleColor(ImGuiCol_PlotHistogram, levelColor);
            ImGui::PushStyleColor(ImGuiCol_FrameBg, Theme::WithAlpha(theme.surface, 0.5f));
            
            char levelText[32];
            snprintf(levelText, sizeof(levelText), "%.0f%%", m_securityLevel * 100.0f);
            ImGui::ProgressBar(m_securityLevel, ImVec2(150.0f, 12.0f), levelText);
            
            ImGui::PopStyleColor(2);
            
            // Security score text
            ImGui::SetCursorPos(ImVec2(windowWidth - 180.0f, 85.0f));
            ImGui::PushStyleColor(ImGuiCol_Text, levelColor);
            if (m_securityLevel >= 0.8f) {
                ImGui::Text("Excellent Security");
            } else if (m_securityLevel >= 0.6f) {
                ImGui::Text("Good Security");
            } else if (m_securityLevel >= 0.4f) {
                ImGui::Text("Moderate Security");
            } else if (m_securityLevel >= 0.2f) {
                ImGui::Text("Poor Security");
            } else {
                ImGui::Text("Critical Risk");
            }
            ImGui::PopStyleColor();
        }
        ImGui::EndChild();
        
        ImGui::PopStyleColor(2); // ChildBg, Border
        ImGui::PopStyleVar(2); // ChildRounding, ChildBorderSize
        
        ImGui::Spacing();
        
        // Security recommendations section
        if (!m_recommendations.empty()) {
            ImGui::PushStyleColor(ImGuiCol_Text, theme.text);
            ImGui::Text("Security Recommendations (%zu)", m_recommendations.size());
            ImGui::PopStyleColor();
            
            ImGui::Spacing();
            
            // Render recommendation cards
            for (size_t i = 0; i < m_recommendations.size(); ++i) {
                RenderRecommendationCard(m_recommendations[i], (int)i);
                if (i < m_recommendations.size() - 1) {
                    ImGui::Spacing();
                }
            }
        }
    }

    void SecurityStatusSummary::Update(float deltaTime) {
        m_animationTime += deltaTime;
    }

    void SecurityStatusSummary::SetRecommendations(const std::vector<SecurityRecommendation>& recommendations) {
        m_recommendations = recommendations;
    }

    void SecurityStatusSummary::AddRecommendation(const SecurityRecommendation& recommendation) {
        m_recommendations.push_back(recommendation);
    }

    std::vector<SecurityRecommendation> SecurityStatusSummary::GenerateRecommendations(const std::vector<SecurityCheck>& checks) {
        std::vector<SecurityRecommendation> recommendations;
        
        for (const auto& check : checks) {
            switch (check.status) {
                case StatusIndicator::Status::Critical:
                    if (check.name.find("Debugger") != std::string::npos) {
                        recommendations.emplace_back(
                            "Debugger Detected",
                            "A debugger or analysis tool has been detected attached to the application.",
                            "Close all debugging tools and restart the application",
                            StatusIndicator::Status::Critical
                        );
                    } else if (check.name.find("Hook") != std::string::npos) {
                        recommendations.emplace_back(
                            "API Hooks Detected",
                            "Suspicious API hooks have been detected that may compromise security.",
                            "Scan system for malware and remove suspicious software",
                            StatusIndicator::Status::Critical
                        );
                    } else if (check.name.find("Integrity") != std::string::npos) {
                        recommendations.emplace_back(
                            "Integrity Violation",
                            "Application integrity has been compromised.",
                            "Reinstall the application from a trusted source",
                            StatusIndicator::Status::Critical
                        );
                    }
                    break;
                    
                case StatusIndicator::Status::Warning:
                    if (check.name.find("Memory") != std::string::npos) {
                        recommendations.emplace_back(
                            "Memory Protection Warning",
                            "Memory protection mechanisms may not be fully effective.",
                            "Enable DEP/ASLR and update system security settings",
                            StatusIndicator::Status::Warning
                        );
                    } else if (check.name.find("Network") != std::string::npos) {
                        recommendations.emplace_back(
                            "Network Security Warning",
                            "Suspicious network activity has been detected.",
                            "Review network connections and firewall settings",
                            StatusIndicator::Status::Warning
                        );
                    }
                    break;
                    
                case StatusIndicator::Status::Unknown:
                    recommendations.emplace_back(
                        "Initialize Security Checks",
                        "Some security checks have not been initialized properly.",
                        "Restart the application to initialize all security systems",
                        StatusIndicator::Status::Warning
                    );
                    break;
                    
                default:
                    break;
            }
        }
        
        // Add general recommendations based on overall security level
        if (m_securityLevel < 0.5f) {
            recommendations.emplace_back(
                "Improve System Security",
                "Your system's security level is below recommended standards.",
                "Update antivirus, enable Windows Defender, and install security updates",
                StatusIndicator::Status::Warning
            );
        }
        
        // Add proactive recommendations for secure systems
        if (m_overallStatus == StatusIndicator::Status::Secure && recommendations.empty()) {
            recommendations.emplace_back(
                "Maintain Security",
                "Your system is currently secure. Continue following best practices.",
                "Keep software updated and run regular security scans",
                StatusIndicator::Status::Secure,
                false // Not actionable, just informational
            );
        }
        
        return recommendations;
    }

    ImVec4 SecurityStatusSummary::GetSeverityColor(StatusIndicator::Status severity) const {
        const auto& theme = Theme::GetCurrentTheme();
        switch (severity) {
            case StatusIndicator::Status::Secure: return theme.success;
            case StatusIndicator::Status::Warning: return theme.warning;
            case StatusIndicator::Status::Critical: return theme.error;
            case StatusIndicator::Status::Checking: return theme.warning;
            default: return theme.textSecondary;
        }
    }

    const char* SecurityStatusSummary::GetSeverityIcon(StatusIndicator::Status severity) const {
        switch (severity) {
            case StatusIndicator::Status::Secure: return "[OK]";
            case StatusIndicator::Status::Warning: return "[!]";
            case StatusIndicator::Status::Critical: return "[X]";
            case StatusIndicator::Status::Checking: return "[~]";
            default: return "[i]";
        }
    }

    void SecurityStatusSummary::RenderRecommendationCard(const SecurityRecommendation& rec, int index) {
        const auto& theme = Theme::GetCurrentTheme();
        
        // Card styling
        ImVec4 cardBg = theme.surface;
        ImVec4 borderColor = GetSeverityColor(rec.severity);
        
        ImGui::PushStyleColor(ImGuiCol_ChildBg, cardBg);
        ImGui::PushStyleColor(ImGuiCol_Border, Theme::WithAlpha(borderColor, 0.6f));
        ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 2.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, theme.cornerRadius);
        
        std::string cardId = "recommendation_" + std::to_string(index);
        if (ImGui::BeginChild(cardId.c_str(), ImVec2(0, 80.0f), true)) {
            // Severity icon and title
            ImGui::SetCursorPos(ImVec2(12.0f, 12.0f));
            ImGui::PushStyleColor(ImGuiCol_Text, borderColor);
            ImGui::Text("%s", GetSeverityIcon(rec.severity));
            ImGui::PopStyleColor();
            
            ImGui::SameLine();
            ImGui::SetCursorPosY(16.0f);
            ImGui::PushStyleColor(ImGuiCol_Text, theme.text);
            ImGui::Text("%s", rec.title.c_str());
            ImGui::PopStyleColor();
            
            // Description
            ImGui::SetCursorPos(ImVec2(12.0f, 35.0f));
            ImGui::PushStyleColor(ImGuiCol_Text, theme.textSecondary);
            ImGui::PushTextWrapPos(ImGui::GetWindowWidth() - 140.0f);
            ImGui::TextWrapped("%s", rec.description.c_str());
            ImGui::PopTextWrapPos();
            ImGui::PopStyleColor();
            
            // Action button (if actionable)
            if (rec.isActionable) {
                float buttonWidth = 120.0f;
                ImGui::SetCursorPos(ImVec2(ImGui::GetWindowWidth() - buttonWidth - 12.0f, 25.0f));
                
                ModernButton actionButton("action_" + std::to_string(index), "Take Action");
                actionButton.SetStyle(rec.severity == StatusIndicator::Status::Critical ? 
                                    ModernButton::Style::Danger : ModernButton::Style::Secondary);
                
                if (actionButton.Render(ImVec2(buttonWidth, 30.0f))) {
                    // Action button clicked - could trigger specific actions
                    // For now, just show the action text
                    ImGui::OpenPopup(("Action##" + std::to_string(index)).c_str());
                }
                
                // Action popup
                if (ImGui::BeginPopup(("Action##" + std::to_string(index)).c_str())) {
                    ImGui::PushStyleColor(ImGuiCol_Text, theme.text);
                    ImGui::Text("Recommended Action:");
                    ImGui::PopStyleColor();
                    
                    ImGui::Separator();
                    
                    ImGui::PushStyleColor(ImGuiCol_Text, theme.textSecondary);
                    ImGui::PushTextWrapPos(300.0f);
                    ImGui::TextWrapped("%s", rec.action.c_str());
                    ImGui::PopTextWrapPos();
                    ImGui::PopStyleColor();
                    
                    ImGui::EndPopup();
                }
            } else {
                // Info icon for non-actionable recommendations
                ImGui::SetCursorPos(ImVec2(ImGui::GetWindowWidth() - 30.0f, 30.0f));
                ImGui::PushStyleColor(ImGuiCol_Text, Theme::WithAlpha(theme.textSecondary, 0.7f));
                ImGui::Text("[i]");
                ImGui::PopStyleColor();
            }
        }
        ImGui::EndChild();
        
        ImGui::PopStyleVar(2); // ChildRounding, ChildBorderSize
        ImGui::PopStyleColor(2); // ChildBg, Border
    }}
 // namespace ui