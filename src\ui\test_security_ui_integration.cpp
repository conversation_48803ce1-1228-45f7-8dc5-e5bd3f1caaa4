#include "security_integration.hpp"
#include "components.hpp"
#include "ui.hpp"
#include <iostream>
#include <cassert>
#include <chrono>
#include <thread>

// Prevent Windows macro conflicts
#ifdef min
#undef min
#endif
#ifdef max
#undef max
#endif

namespace ui {

    // Integration test suite for security-UI connection
    class SecurityUIIntegrationTestSuite {
    private:
        static void AssertTrue(bool condition, const std::string& message) {
            if (!condition) {
                throw std::runtime_error("Integration test failed: " + message);
            }
        }

        static void AssertEqual(StatusIndicator::Status expected, StatusIndicator::Status actual, const std::string& message) {
            if (expected != actual) {
                throw std::runtime_error("Integration test failed: " + message + 
                    " (expected: " + std::to_string(static_cast<int>(expected)) + 
                    ", actual: " + std::to_string(static_cast<int>(actual)) + ")");
            }
        }

    public:
        // Test security integration initialization
        static void TestSecurityIntegrationInitialization() {
            std::cout << "Testing security integration initialization..." << std::endl;
            
            SecurityIntegration& security = SecurityIntegration::GetInstance();
            
            // Initialize the security system
            security.Initialize();
            
            // Verify all expected security checks are registered
            auto checkResults = security.GetAllCheckResults();
            AssertTrue(checkResults.size() >= 6, "All security checks should be registered");
            
            // Verify specific checks exist
            bool hasDebuggerCheck = false;
            bool hasVMCheck = false;
            bool hasHookCheck = false;
            bool hasIntegrityCheck = false;
            
            for (const auto& result : checkResults) {
                if (result.name == "Debugger Detection") hasDebuggerCheck = true;
                if (result.name == "VM Detection") hasVMCheck = true;
                if (result.name == "Hook Detection") hasHookCheck = true;
                if (result.name == "Integrity Check") hasIntegrityCheck = true;
            }
            
            AssertTrue(hasDebuggerCheck, "Debugger detection check should be registered");
            AssertTrue(hasVMCheck, "VM detection check should be registered");
            AssertTrue(hasHookCheck, "Hook detection check should be registered");
            AssertTrue(hasIntegrityCheck, "Integrity check should be registered");
            
            std::cout << "✓ Security integration initialization tests passed" << std::endl;
        }

        // Test UI-Security dashboard connection
        static void TestDashboardConnection() {
            std::cout << "Testing dashboard connection..." << std::endl;
            
            SecurityIntegration& security = SecurityIntegration::GetInstance();
            
            // Create a test dashboard
            auto dashboard = std::make_unique<SecurityDashboard>();
            
            // Connect security system to dashboard
            security.ConnectToSecurityDashboard(dashboard.get());
            
            // Run security checks to populate dashboard
            security.RunAllSecurityChecks();
            
            // Wait a moment for async operations
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
            // Verify dashboard has been updated
            // Note: In a real test, we'd verify the dashboard's internal state
            // For now, we just verify the connection doesn't crash
            
            std::cout << "✓ Dashboard connection tests passed" << std::endl;
        }

        // Test real-time security updates
        static void TestRealTimeUpdates() {
            std::cout << "Testing real-time security updates..." << std::endl;
            
            SecurityIntegration& security = SecurityIntegration::GetInstance();
            
            // Set up event callback to track updates
            bool eventReceived = false;
            std::string lastEventCheck;
            StatusIndicator::Status lastEventStatus;
            
            security.RegisterEventCallback([&](const SecurityEvent& event) {
                eventReceived = true;
                lastEventCheck = event.checkName;
                lastEventStatus = event.status;
            });
            
            // Run a specific security check
            security.RunSecurityCheck("Debugger Detection");
            
            // Wait for event processing
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
            
            // Verify event was received
            AssertTrue(eventReceived, "Security event should be received");
            AssertTrue(lastEventCheck == "Debugger Detection", "Event should be for debugger detection");
            
            // Test update callback
            bool updateReceived = false;
            security.RegisterUpdateCallback([&](const std::vector<SecurityCheckResult>& results) {
                updateReceived = true;
            });
            
            // Run another check
            security.RunSecurityCheck("VM Detection");
            
            // Wait for update processing
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
            
            // Verify update was received
            AssertTrue(updateReceived, "Security update should be received");
            
            std::cout << "✓ Real-time security update tests passed" << std::endl;
        }

        // Test security status mapping
        static void TestStatusMapping() {
            std::cout << "Testing security status mapping..." << std::endl;
            
            SecurityIntegration& security = SecurityIntegration::GetInstance();
            
            // Run all checks to get current status
            security.RunAllSecurityChecks();
            
            // Get overall security status
            StatusIndicator::Status overallStatus = security.GetOverallSecurityStatus();
            
            // Verify status is valid
            AssertTrue(overallStatus == StatusIndicator::Status::Secure ||
                      overallStatus == StatusIndicator::Status::Warning ||
                      overallStatus == StatusIndicator::Status::Critical ||
                      overallStatus == StatusIndicator::Status::Unknown,
                      "Overall status should be valid");
            
            // Get security score
            float securityScore = security.GetSecurityScore();
            AssertTrue(securityScore >= 0.0f && securityScore <= 1.0f, 
                      "Security score should be between 0.0 and 1.0");
            
            // Test individual check results
            auto checkResults = security.GetAllCheckResults();
            for (const auto& result : checkResults) {
                AssertTrue(!result.name.empty(), "Check name should not be empty");
                AssertTrue(!result.description.empty(), "Check description should not be empty");
                AssertTrue(result.executionTime >= 0.0f, "Execution time should be non-negative");
            }
            
            std::cout << "✓ Security status mapping tests passed" << std::endl;
        }

        // Test notification system integration
        static void TestNotificationIntegration() {
            std::cout << "Testing notification system integration..." << std::endl;
            
            SecurityNotificationSystem& notifications = SecurityNotificationSystem::GetInstance();
            
            // Clear existing notifications
            notifications.ClearAllNotifications();
            
            // Add test notifications
            notifications.AddNotification(
                SecurityNotificationSystem::NotificationType::Info,
                "Test Info",
                "This is a test info notification"
            );
            
            notifications.AddNotification(
                SecurityNotificationSystem::NotificationType::Critical,
                "Test Critical",
                "This is a test critical notification",
                0.0f // Persistent
            );
            
            // Verify notifications were added
            auto allNotifications = notifications.GetAllNotifications();
            AssertTrue(allNotifications.size() == 2, "Two notifications should be added");
            
            // Verify unread count
            size_t unreadCount = notifications.GetUnreadCount();
            AssertTrue(unreadCount == 2, "Two notifications should be unread");
            
            // Mark one as read
            notifications.MarkAsRead(0);
            unreadCount = notifications.GetUnreadCount();
            AssertTrue(unreadCount == 1, "One notification should remain unread");
            
            // Test active notifications (non-expired)
            auto activeNotifications = notifications.GetActiveNotifications();
            AssertTrue(activeNotifications.size() >= 1, "At least one notification should be active");
            
            std::cout << "✓ Notification system integration tests passed" << std::endl;
        }

        // Test security monitoring lifecycle
        static void TestMonitoringLifecycle() {
            std::cout << "Testing security monitoring lifecycle..." << std::endl;
            
            SecurityIntegration& security = SecurityIntegration::GetInstance();
            
            // Verify monitoring is not active initially
            AssertTrue(!security.IsMonitoringActive(), "Monitoring should not be active initially");
            
            // Start monitoring
            security.StartRealTimeMonitoring();
            AssertTrue(security.IsMonitoringActive(), "Monitoring should be active after start");
            
            // Wait for monitoring to run
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
            
            // Stop monitoring
            security.StopRealTimeMonitoring();
            AssertTrue(!security.IsMonitoringActive(), "Monitoring should not be active after stop");
            
            std::cout << "✓ Security monitoring lifecycle tests passed" << std::endl;
        }

        // Test configuration persistence
        static void TestConfigurationPersistence() {
            std::cout << "Testing configuration persistence..." << std::endl;
            
            SecurityIntegration& security = SecurityIntegration::GetInstance();
            
            // Test auto-run configuration
            security.SetAutoRunEnabled(false);
            AssertTrue(!security.IsAutoRunEnabled(), "Auto-run should be disabled");
            
            security.SetAutoRunEnabled(true);
            AssertTrue(security.IsAutoRunEnabled(), "Auto-run should be enabled");
            
            // Test check interval configuration
            security.SetCheckInterval(std::chrono::milliseconds(2000));
            
            // Test individual check enable/disable
            security.EnableSecurityCheck("Debugger Detection", false);
            auto result = security.GetCheckResult("Debugger Detection");
            AssertTrue(!result.isEnabled, "Debugger detection should be disabled");
            
            security.EnableSecurityCheck("Debugger Detection", true);
            result = security.GetCheckResult("Debugger Detection");
            AssertTrue(result.isEnabled, "Debugger detection should be enabled");
            
            std::cout << "✓ Configuration persistence tests passed" << std::endl;
        }

        // Run all integration tests
        static void RunAllIntegrationTests() {
            std::cout << "=== Running Security-UI Integration Tests ===" << std::endl;
            
            try {
                TestSecurityIntegrationInitialization();
                TestDashboardConnection();
                TestRealTimeUpdates();
                TestStatusMapping();
                TestNotificationIntegration();
                TestMonitoringLifecycle();
                TestConfigurationPersistence();
                
                std::cout << "\n✓ All security-UI integration tests passed!" << std::endl;
            }
            catch (const std::exception& e) {
                std::cerr << "\n✗ Integration test failed with exception: " << e.what() << std::endl;
                throw;
            }
            catch (...) {
                std::cerr << "\n✗ Integration test failed with unknown exception" << std::endl;
                throw;
            }
        }
    };

} // namespace ui

// Note: To run these tests, call ui::SecurityUIIntegrationTestSuite::RunAllIntegrationTests()
// from your main application or create a separate test executable
