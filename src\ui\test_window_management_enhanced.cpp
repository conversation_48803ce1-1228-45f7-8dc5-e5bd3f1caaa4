#include "ui.hpp"
#include "components.hpp"
#include <iostream>
#include <cassert>
#include <filesystem>
#include <chrono>
#include <thread>

// Prevent Windows macro conflicts
#ifdef min
#undef min
#endif
#ifdef max
#undef max
#endif

namespace ui {

    // Enhanced test suite for window management functionality
    class WindowManagementTestSuite {
    private:
        static void AssertTrue(bool condition, const std::string& message) {
            if (!condition) {
                throw std::runtime_error("Assertion failed: " + message);
            }
        }

        static void AssertEqual(float expected, float actual, const std::string& message, float tolerance = 0.1f) {
            if (std::abs(expected - actual) > tolerance) {
                throw std::runtime_error("Assertion failed: " + message + 
                    " (expected: " + std::to_string(expected) + ", actual: " + std::to_string(actual) + ")");
            }
        }

        static void AssertEqual(const ImVec2& expected, const ImVec2& actual, const std::string& message, float tolerance = 0.1f) {
            AssertEqual(expected.x, actual.x, message + " (x)", tolerance);
            AssertEqual(expected.y, actual.y, message + " (y)", tolerance);
        }

    public:
        // Test LayoutManager functionality
        static void TestLayoutManager() {
            std::cout << "Testing LayoutManager functionality..." << std::endl;
            
            // Test responsive breakpoint detection
            bool isResponsive = LayoutManager::IsResponsiveBreakpoint();
            std::cout << "  Responsive breakpoint: " << (isResponsive ? "true" : "false") << std::endl;
            
            // Test header height calculation
            float headerHeight = LayoutManager::CalculateResponsiveHeaderHeight();
            AssertTrue(headerHeight > 0, "Header height should be positive");
            std::cout << "  Header height: " << headerHeight << "px" << std::endl;
            
            // Test footer height calculation
            float footerHeight = LayoutManager::CalculateResponsiveFooterHeight();
            AssertTrue(footerHeight > 0, "Footer height should be positive");
            std::cout << "  Footer height: " << footerHeight << "px" << std::endl;
            
            // Test padding calculation
            float padding = LayoutManager::CalculateResponsivePadding();
            AssertTrue(padding > 0, "Padding should be positive");
            std::cout << "  Padding: " << padding << "px" << std::endl;
            
            std::cout << "✓ LayoutManager tests passed" << std::endl;
        }

        // Test enhanced window controls
        static void TestEnhancedWindowControls() {
            std::cout << "Testing enhanced window controls..." << std::endl;
            
            WindowManager& wm = WindowManager::GetInstance();
            
            // Test window state queries
            bool isMaximized = wm.IsWindowMaximized();
            bool isMinimized = wm.IsWindowMinimized();
            bool isVisible = wm.IsWindowVisible();
            
            std::cout << "  Window state - Maximized: " << isMaximized 
                      << ", Minimized: " << isMinimized 
                      << ", Visible: " << isVisible << std::endl;
            
            // Test aspect ratio functionality
            float originalRatio = wm.GetWindowAspectRatio();
            wm.SetWindowAspectRatio(16.0f / 9.0f, true);
            AssertEqual(16.0f / 9.0f, wm.GetWindowAspectRatio(), "Aspect ratio setting");
            AssertTrue(wm.IsAspectRatioMaintained(), "Aspect ratio should be maintained");
            
            // Restore original ratio
            wm.SetWindowAspectRatio(originalRatio, false);
            
            // Test window centering
            ImVec2 originalPos = wm.GetWindowPosition();
            wm.CenterWindow();
            AssertTrue(wm.IsWindowCentered(), "Window should be centered");
            
            // Restore original position
            wm.SetWindowPosition(originalPos);
            
            std::cout << "✓ Enhanced window controls tests passed" << std::endl;
        }

        // Test window state persistence with enhanced features
        static void TestEnhancedStatePersistence() {
            std::cout << "Testing enhanced state persistence..." << std::endl;
            
            const std::string testFile = "test_enhanced_window_state.dat";
            
            // Clean up any existing test file
            if (std::filesystem::exists(testFile)) {
                std::filesystem::remove(testFile);
            }
            
            WindowManager& wm = WindowManager::GetInstance();
            
            // Set comprehensive test state
            ImVec2 testPos(350, 275);
            ImVec2 testSize(950, 750);
            ImVec2 testMinSize(550, 450);
            ImVec2 testMaxSize(1450, 1050);
            float testAspectRatio = 4.0f / 3.0f;
            
            wm.SetWindowPosition(testPos);
            wm.SetWindowSize(testSize);
            wm.SetMinWindowSize(testMinSize);
            wm.SetMaxWindowSize(testMaxSize);
            wm.SetWindowAspectRatio(testAspectRatio, true);
            wm.SetAutoSave(true);
            
            // Add multiple presets
            wm.SetWindowPositionPreset("preset_1", ImVec2(100, 100), ImVec2(800, 600));
            wm.SetWindowPositionPreset("preset_2", ImVec2(200, 150), ImVec2(1024, 768));
            wm.SetWindowPositionPreset("preset_3", ImVec2(300, 200), ImVec2(1280, 720));
            
            // Save state
            AssertTrue(wm.SaveWindowState(testFile), "Save enhanced state");
            AssertTrue(std::filesystem::exists(testFile), "Enhanced state file exists");
            
            // Modify values
            wm.SetWindowPosition(ImVec2(50, 50));
            wm.SetWindowSize(ImVec2(500, 400));
            wm.SetWindowAspectRatio(16.0f / 9.0f, false);
            wm.SetAutoSave(false);
            wm.RemoveWindowPositionPreset("preset_2");
            
            // Load state
            AssertTrue(wm.LoadWindowState(testFile), "Load enhanced state");
            
            // Verify loaded values
            AssertEqual(testPos, wm.GetWindowPosition(), "Loaded position");
            AssertEqual(testSize, wm.GetWindowSize(), "Loaded size");
            AssertEqual(testMinSize, wm.GetMinWindowSize(), "Loaded min size");
            AssertEqual(testMaxSize, wm.GetMaxWindowSize(), "Loaded max size");
            AssertEqual(testAspectRatio, wm.GetWindowAspectRatio(), "Loaded aspect ratio");
            AssertTrue(wm.IsAspectRatioMaintained(), "Aspect ratio maintenance loaded");
            AssertTrue(wm.IsAutoSaveEnabled(), "Auto-save state loaded");
            
            // Verify presets
            std::vector<std::string> presets = wm.GetWindowPositionPresets();
            AssertTrue(presets.size() == 3, "All presets loaded");
            
            // Clean up
            std::filesystem::remove(testFile);
            
            std::cout << "✓ Enhanced state persistence tests passed" << std::endl;
        }

        // Test performance of window management operations
        static void TestWindowManagementPerformance() {
            std::cout << "Testing window management performance..." << std::endl;
            
            WindowManager& wm = WindowManager::GetInstance();
            
            // Test rapid position updates
            auto startTime = std::chrono::high_resolution_clock::now();
            
            for (int i = 0; i < 1000; ++i) {
                wm.SetWindowPosition(ImVec2(100 + i % 100, 100 + i % 100));
            }
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
            
            std::cout << "  1000 position updates took: " << duration.count() << " microseconds" << std::endl;
            AssertTrue(duration.count() < 10000, "Position updates should be fast"); // Less than 10ms
            
            // Test rapid size updates
            startTime = std::chrono::high_resolution_clock::now();
            
            for (int i = 0; i < 1000; ++i) {
                wm.SetWindowSize(ImVec2(800 + i % 200, 600 + i % 150));
            }
            
            endTime = std::chrono::high_resolution_clock::now();
            duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
            
            std::cout << "  1000 size updates took: " << duration.count() << " microseconds" << std::endl;
            AssertTrue(duration.count() < 10000, "Size updates should be fast"); // Less than 10ms
            
            std::cout << "✓ Window management performance tests passed" << std::endl;
        }

        // Test window constraints and validation
        static void TestWindowConstraintsValidation() {
            std::cout << "Testing window constraints and validation..." << std::endl;
            
            WindowManager& wm = WindowManager::GetInstance();
            
            // Set test constraints
            ImVec2 minSize(400, 300);
            ImVec2 maxSize(1600, 1200);
            wm.SetMinWindowSize(minSize);
            wm.SetMaxWindowSize(maxSize);
            
            // Test size validation
            AssertTrue(wm.IsValidWindowSize(ImVec2(800, 600)), "Valid size should pass");
            AssertTrue(!wm.IsValidWindowSize(ImVec2(200, 150)), "Too small size should fail");
            AssertTrue(!wm.IsValidWindowSize(ImVec2(2000, 1500)), "Too large size should fail");
            
            // Test position validation
            AssertTrue(wm.IsValidWindowPosition(ImVec2(100, 100)), "Valid position should pass");
            AssertTrue(!wm.IsValidWindowPosition(ImVec2(-200, -200)), "Invalid position should fail");
            
            // Test constraint enforcement
            wm.SetWindowSize(ImVec2(200, 150)); // Too small
            wm.EnforceWindowConstraints();
            ImVec2 constrainedSize = wm.GetWindowSize();
            AssertTrue(constrainedSize.x >= minSize.x && constrainedSize.y >= minSize.y, 
                      "Size should be constrained to minimum");
            
            std::cout << "✓ Window constraints validation tests passed" << std::endl;
        }

        // Run all enhanced tests
        static void RunAllEnhancedTests() {
            std::cout << "=== Running Enhanced Window Management Tests ===" << std::endl;
            
            try {
                TestLayoutManager();
                TestEnhancedWindowControls();
                TestEnhancedStatePersistence();
                TestWindowManagementPerformance();
                TestWindowConstraintsValidation();
                
                std::cout << "\n✓ All enhanced window management tests passed!" << std::endl;
            }
            catch (const std::exception& e) {
                std::cerr << "\n✗ Enhanced test failed with exception: " << e.what() << std::endl;
                throw;
            }
            catch (...) {
                std::cerr << "\n✗ Enhanced test failed with unknown exception" << std::endl;
                throw;
            }
        }
    };

} // namespace ui

// Note: To run these tests, call ui::WindowManagementTestSuite::RunAllEnhancedTests()
// from your main application or create a separate test executable
