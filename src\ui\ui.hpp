#pragma once

#include <d3d9.h>
#include "../../external/imgui/imgui.h"
#include <string>
#include <vector>
#include <memory>
#include <chrono>
#include <map>
#include <functional>
#include <fstream>

// UI Namespace for all UI-related functionality
namespace ui {

    // Forward declarations
    class UIComponent;
    class UITheme;
    class AnimationSystem;

    // Base UI Component Interface
    class UIComponent {
    public:
        virtual ~UIComponent() = default;
        virtual void Render() = 0;
        virtual void Update(float deltaTime) {}
        virtual void SetEnabled(bool enabled) { m_enabled = enabled; }
        virtual bool IsEnabled() const { return m_enabled; }

    protected:
        bool m_enabled = true;
    };

    // UI State Management
    class UIState {
    public:
        // Window state
        bool isMainWindowVisible = true;
        ImVec2 windowSize = ImVec2(800, 600);
        ImVec2 windowPosition = ImVec2(100, 100);
        ImVec2 minWindowSize = ImVec2(600, 400);
        ImVec2 maxWindowSize = ImVec2(1920, 1080);
        
        // Layout state
        float headerHeight = 60.0f;
        float footerHeight = 30.0f;
        float contentPadding = 16.0f;
        bool showFooter = true;
        bool showHeader = true;
        
        // Animation state
        float globalAnimationTime = 0.0f;
        std::map<std::string, float> componentAnimations;
        
        // User preferences
        bool enableAnimations = true;
        float uiScale = 1.0f;
        
        // Window management
        bool windowResized = false;
        bool windowMoved = false;
        ImVec2 lastWindowSize = ImVec2(800, 600);
        ImVec2 lastWindowPosition = ImVec2(100, 100);
    };

    // Layout Management
    class LayoutManager {
    public:
        static void BeginMainLayout();
        static void EndMainLayout();
        static ImVec2 GetContentAreaSize();
        static ImVec2 GetContentAreaPosition();
        static void HandleWindowResize();
        static bool IsResponsiveBreakpoint();

        // Enhanced layout utilities
        static void SetLayoutMode(bool compactMode);
        static bool IsCompactMode();
        static float GetScaleFactor();
        static void SetScaleFactor(float scale);

        // Public for testing
        static float CalculateResponsiveHeaderHeight();
        static float CalculateResponsiveFooterHeight();
        static float CalculateResponsivePadding();

    private:

        static bool s_compactMode;
        static float s_scaleFactor;
    };

    // Window Management Utilities
    class WindowUtils {
    public:
        // Screen and monitor utilities
        static ImVec2 GetPrimaryScreenSize();
        static ImVec2 GetWorkAreaSize();
        static std::vector<ImVec2> GetAllScreenSizes();
        static int GetScreenCount();

        // Window positioning helpers
        static ImVec2 CalculateCenteredPosition(const ImVec2& windowSize, const ImVec2& screenSize);
        static ImVec2 CalculateOptimalSize(const ImVec2& contentSize, float aspectRatio = 0.0f);
        static bool IsPositionOnScreen(const ImVec2& position, const ImVec2& windowSize);

        // Window state helpers
        static std::string WindowStateToString(bool isMaximized, bool isMinimized, bool isVisible);
        static ImVec2 ClampToScreen(const ImVec2& position, const ImVec2& windowSize);

    private:
        static ImVec2 s_primaryScreenSize;
        static bool s_screenSizeCached;
    };

    // Main UI functions
    void init(LPDIRECT3DDEVICE9 device);
    void render();
    void shutdown();
    
    // UI State access
    UIState& GetUIState();
    
    // Theme management
    void ApplyTheme();
    void SetDarkTheme();
    
    // Window management
    void SaveWindowState();
    void LoadWindowState();
    void HandleWindowEvents();
    
    // Window management class
    class WindowManager {
    public:
        static WindowManager& GetInstance();
        
        // Window positioning and sizing controls
        void SetWindowPosition(const ImVec2& position);
        void SetWindowSize(const ImVec2& size);
        ImVec2 GetWindowPosition() const;
        ImVec2 GetWindowSize() const;
        void SetMinWindowSize(const ImVec2& minSize);
        void SetMaxWindowSize(const ImVec2& maxSize);
        ImVec2 GetMinWindowSize() const;
        ImVec2 GetMaxWindowSize() const;
        
        // Advanced window controls
        void CenterWindow();
        void MaximizeWindow();
        void RestoreWindow();
        void MinimizeWindow();
        void MoveWindowToScreen(int screenIndex);
        void FitWindowToContent(const ImVec2& contentSize);
        void SetWindowAspectRatio(float ratio, bool maintain = true);
        float GetWindowAspectRatio() const;
        
        // Window positioning presets
        void SetWindowPositionPreset(const std::string& presetName, const ImVec2& position, const ImVec2& size);
        bool ApplyWindowPositionPreset(const std::string& presetName);
        void RemoveWindowPositionPreset(const std::string& presetName);
        std::vector<std::string> GetWindowPositionPresets() const;
        
        // Window constraints
        void SetWindowConstraints(const ImVec2& minSize, const ImVec2& maxSize);
        ImVec2 GetConstrainedSize(const ImVec2& size) const;
        ImVec2 GetConstrainedPosition(const ImVec2& position) const;
        ImVec2 GetConstrainedPosition(const ImVec2& position, const ImVec2& windowSize) const;
        
        // Window state persistence
        bool SaveWindowState(const std::string& filename = "window_state.dat");
        bool LoadWindowState(const std::string& filename = "window_state.dat");
        void SetAutoSave(bool autoSave) { m_autoSave = autoSave; }
        bool IsAutoSave() const { return m_autoSave; }
        bool IsAutoSaveEnabled() const { return m_autoSave; } // Alias for compatibility
        
        // Window resize handling
        void HandleWindowResize();
        void RegisterResizeCallback(std::function<void(ImVec2)> callback);
        void UnregisterResizeCallback();
        bool IsWindowResized() const { return m_windowResized; }
        void ClearResizeFlag() { m_windowResized = false; }
        
        // Layout updates
        void UpdateLayout();
        void SetLayoutUpdateCallback(std::function<void()> callback);
        void TriggerLayoutUpdate();
        
        // Window state queries
        bool IsWindowMinimized() const;
        bool IsWindowMaximized() const;
        bool IsWindowVisible() const;
        void SetWindowVisible(bool visible);
        bool IsWindowCentered() const;
        bool IsAspectRatioMaintained() const;
        
        // Window constraints
        void EnforceWindowConstraints();
        bool IsValidWindowSize(const ImVec2& size) const;
        bool IsValidWindowPosition(const ImVec2& position) const;
        ImVec2 ClampWindowSize(const ImVec2& size) const;
        ImVec2 ClampWindowPosition(const ImVec2& position) const;
        
        // Update method
        void Update(float deltaTime);
        
    private:
        WindowManager() = default;
        ~WindowManager() = default;
        WindowManager(const WindowManager&) = delete;
        WindowManager& operator=(const WindowManager&) = delete;
        
        // Window state
        ImVec2 m_windowPosition = ImVec2(100, 100);
        ImVec2 m_windowSize = ImVec2(800, 600);
        ImVec2 m_minWindowSize = ImVec2(600, 400);
        ImVec2 m_maxWindowSize = ImVec2(1920, 1080);
        ImVec2 m_lastWindowPosition = ImVec2(100, 100);
        ImVec2 m_lastWindowSize = ImVec2(800, 600);
        ImVec2 m_restoredPosition = ImVec2(100, 100);
        ImVec2 m_restoredSize = ImVec2(800, 600);
        
        // State flags
        bool m_windowResized = false;
        bool m_windowMoved = false;
        bool m_windowVisible = true;
        bool m_autoSave = true;
        bool m_isMaximized = false;
        bool m_isMinimized = false;
        bool m_maintainAspectRatio = false;
        float m_aspectRatio = 4.0f / 3.0f;
        
        // Window position presets
        std::map<std::string, std::pair<ImVec2, ImVec2>> m_positionPresets;
        
        // Callbacks
        std::function<void(ImVec2)> m_resizeCallback;
        std::function<void()> m_layoutUpdateCallback;
        
        // Persistence
        struct WindowStateData {
            float posX, posY;
            float sizeX, sizeY;
            float minSizeX, minSizeY;
            float maxSizeX, maxSizeY;
            float restoredPosX, restoredPosY;
            float restoredSizeX, restoredSizeY;
            bool visible;
            bool isMaximized;
            bool isMinimized;
            bool maintainAspectRatio;
            float aspectRatio;
            uint32_t presetCount;
            uint32_t checksum;
        };
        
        uint32_t CalculateChecksum(const WindowStateData& data) const;
        bool ValidateWindowStateData(const WindowStateData& data) const;
    };

} // namespace ui